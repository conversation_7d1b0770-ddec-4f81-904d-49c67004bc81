import request from '@/utils/request'

/**
 * Dashboard相关API接口
 * 包含性能数据分析平台的所有接口
 */
export function useDashboardApi() {
  return {
    // 获取可用日期列表
    getDate: (params) => {
      return request({
        url: '/api/device/get_date/',
        method: 'get',
        params,
      })
    },

    // 获取设备配置列表（根据日期）
    getDeviceConfig: (params) => {
      return request({
        url: '/api/device/get_config/',
        method: 'get',
        params,
      })
    },

    // 获取指标列表
    getAvailableMetrics: (params) => {
      return request({
        url: '/api/device/get_performance_data/',
        method: 'get',
        params,
      })
    },
    // 获取可用字段列表接口
    getAvailableFields: (params) => {
      return request({
        url: '/api/device/get_fields/',
        method: 'get',
        params,
      })
    },
    // 获取图表数据接口
    getChartData: (data) => {
      return request({
        url: '/api/device/get_graph_data/',
        method: 'POST',
        data,
      })
    },
    // 获取统计数据接口
    getStatsData: (data) => {
      return request({
        url: '/api/device/get_stats_data/',
        method: 'POST',
        data,
      })
    },
    // 获取显存文件接口
    getMemoryFile: (params) => {
      return request({
        url: '/api/device/get_memory_file/',
        method: 'get',
        params,
      })
    },
    // 获取可用指标列表接口
    // getAvailableMetrics: (params) => {
    //   return request({
    //     url: '/api/device/get_metrics/',
    //     method: 'get',
    //     params,
    //   })
    // },

    // 获取GPU显存数据接口
    getGPUMemoryData: (params) => {
      return request({
        url: '/api/device/get_memory_data/',
        method: 'get',
        params,
      })
    },
    // 获取GPU分类详情表格接口
    getGPUMemoryDetail: (params) => {
      return request({
        url: '/api/device/get_memory_list/',
        method: 'get',
        params,
      })
    },
  }
}
