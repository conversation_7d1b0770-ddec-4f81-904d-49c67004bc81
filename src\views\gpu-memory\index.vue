<template>
  <div class="gpu-memory-page">
    <div class="page-header">
      <div class="header-left">
        <el-button @click="goBack" type="primary" plain class="back-button">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <h1>GPU内存分析</h1>
      </div>

      <div class="header-controls">
        <div class="control-group">
          <label for="dateSelect">选择日期:</label>
          <el-select
            v-model="selectedDate"
            placeholder="选择日期"
            @change="onDateChange"
            style="width: 200px"
          >
            <el-option v-for="date in availableDates" :key="date" :label="date" :value="date" />
          </el-select>
        </div>

        <div class="control-group">
          <label for="machineSelect">选择机型:</label>
          <el-select
            v-model="selectedMachine"
            placeholder="选择机型"
            @change="onMachineChange"
            style="width: 220px"
            :disabled="!selectedDate"
          >
            <el-option
              v-for="machine in availableMachines"
              :key="machine"
              :label="machine.label"
              :value="machine.value"
            />
          </el-select>
        </div>

        <div class="control-group">
          <label for="fileSelect">选择文件:</label>
          <el-select
            v-model="selectedFile"
            placeholder="选择文件"
            @change="onFileChange"
            style="width: 250px"
            :disabled="!selectedMachine"
          >
            <el-option v-for="file in availableFiles" :key="file" :label="file" :value="file" />
          </el-select>
        </div>
      </div>
    </div>

    <div class="chart-section">
      <div class="chart-header">
        <div class="chart-tip">
          <el-icon><InfoFilled /></el-icon>
          <span>点击柱状图或下方标签查看详细信息，也可使用左右箭头键切换分类</span>
        </div>
      </div>
      <div
        ref="chartContainer"
        class="chart-container"
        v-loading="chartLoading"
        element-loading-text="数据正在加载中..."
      ></div>
    </div>

    <!-- 点击柱状图后显示的详细表格 -->
    <div class="data-section">
      <div class="table-header">
        <h3>
          {{ currentTableTitle
          }}{{ !tableLoading && totalCount > 0 ? `(共${totalCount}条数据)` : '' }}
        </h3>
        <div class="table-controls">
          <el-input
            v-model="searchKeywords"
            placeholder="输入关键字搜索..."
            class="search-input"
            @blur="handleSearchBlur"
            @clear="handleSearchClear"
            clearable
          />
        </div>
      </div>

      <el-table
        :data="tableData"
        style="width: 100%"
        height="500"
        v-loading="tableLoading"
        element-loading-text="正在加载详细数据..."
        border
      >
        <el-table-column prop="name" label="名称(Name)" width="320" show-overflow-tooltip />
        <el-table-column prop="memory" label="内存(Memory) (MB)" width="180"></el-table-column>
        <el-table-column prop="resolution" label="分辨率(Resolution)" width="180" />
        <el-table-column prop="format" label="格式(Format)" width="170" show-overflow-tooltip />
        <el-table-column prop="type" label="类型(Type)" width="140" />
        <el-table-column prop="owner" label="Owner" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.owner || 'None' }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalCount"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ArrowLeft, InfoFilled } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import { useDashboardApi } from '@/api/dashboaard/index.js'
const { getDate, getMemoryFile, getDeviceConfig, getGPUMemoryData, getGPUMemoryDetail } =
  useDashboardApi()
const router = useRouter()
const route = useRoute()

// 响应式数据
const selectedDate = ref('')
const selectedMachine = ref('')
const selectedFile = ref('')
const availableDates = ref([])
const availableMachines = ref([])
const availableFiles = ref([])
const chartData = ref({})
const currentTableData = ref([])
const currentTableTitle = ref('')
const chartContainer = ref(null)

// 新增表格相关的响应式数据
const tableData = ref([])
const tableLoading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
const selectedCategory = ref('')

// 柱状图加载状态
const chartLoading = ref(false)

// 搜索关键字
const searchKeywords = ref('')

let chartInstance = null

// 方法
const goBack = () => {
  // router.go(-1)
  // 跳转回首页
  router.push('/')
}
// 动态加载数据的方法
const loadAvailableDates = async () => {
  try {
    const response = await getDate()
    if (response.code === 200 && response.data) {
      availableDates.value = response.data
    } else {
      availableDates.value = []
      ElMessage.warning('未获取到可用日期数据')
    }

    // 从URL参数中获取默认日期，如果没有则使用第一个可用日期
    const urlDate = route.query.date
    if (availableDates.value.length > 0) {
      if (urlDate && availableDates.value.includes(urlDate)) {
        selectedDate.value = urlDate
      } else {
        selectedDate.value = availableDates.value[0]
      }
      await loadAvailableMachines()
    }
  } catch (error) {
    console.error('Failed to load available dates:', error)
    ElMessage.error('获取可用日期失败')
    availableDates.value = []
  }
}

const loadAvailableMachines = async () => {
  if (!selectedDate.value) return
  try {
    const response = await getDeviceConfig({ date: selectedDate.value })
    if (response.code === 200 && response.data) {
      availableMachines.value = response.data?.configs?.map((config) => ({
        label: config,
        value: config,
      }))
    } else {
      availableMachines.value = []
      ElMessage.warning('未获取到设备配置数据')
    }
    // 从URL参数中获取默认机器配置，如果没有则使用第一个可用配置
    const urlMachine = route.query.machine
    if (availableMachines.value.length > 0) {
      const machineExists = availableMachines.value.some((machine) => machine.value === urlMachine)
      if (urlMachine && machineExists) {
        selectedMachine.value = urlMachine
      } else {
        selectedMachine.value = availableMachines.value[0].value
      }
      await loadAvailableFiles()
    }
  } catch (error) {
    console.error('Failed to load available machines:', error)
    ElMessage.error('获取设备配置失败')
    availableMachines.value = []
  }
}

const loadAvailableFiles = async () => {
  if (!selectedDate.value || !selectedMachine.value) return

  try {
    const response = await getMemoryFile({
      date: selectedDate.value,
      config: selectedMachine.value,
    })
    if (response && response.data) {
      availableFiles.value = response.data
    } else {
      availableFiles.value = []
      ElMessage.warning('未获取到内存文件数据')
    }

    // 设置默认选中文件：优先选择'rhiDumpResourceMemory-start.csv'，否则选择第一个文件
    if (availableFiles.value.length > 0) {
      const preferredFile = 'rhiDumpResourceMemory-start.csv'
      const hasPreferredFile = availableFiles.value.includes(preferredFile)
      selectedFile.value = hasPreferredFile ? preferredFile : availableFiles.value[0]
      await fetchData()
    }
  } catch (error) {
    console.error('Failed to load available files:', error)
    ElMessage.error('获取内存文件失败')
    availableFiles.value = []
  }
}

// 监听下拉选择变化
const onDateChange = async () => {
  selectedMachine.value = ''
  selectedFile.value = ''
  availableMachines.value = []
  availableFiles.value = []
  currentTableData.value = []

  if (selectedDate.value) {
    await loadAvailableMachines()
    updateURL()
  }
}

const onMachineChange = async () => {
  selectedFile.value = ''
  availableFiles.value = []
  currentTableData.value = []

  if (selectedMachine.value) {
    await loadAvailableFiles()
    updateURL()
  }
}

const onFileChange = async () => {
  if (selectedFile.value) {
    await fetchData()
    updateURL()
  }
}

// 更新URL参数
const updateURL = () => {
  const query = {}
  if (selectedDate.value) query.date = selectedDate.value
  if (selectedMachine.value) query.machine = selectedMachine.value
  router.replace({ query })
}

// 数据获取函数 - 使用新的数据结构
const fetchData = async () => {
  try {
    if (!selectedDate.value || !selectedMachine.value || !selectedFile.value) {
      ElMessage.warning('请先选择日期、机型和文件')
      return
    }

    chartLoading.value = true // 开始加载

    // 使用真实API接口获取GPU内存数据
    const response = await getGPUMemoryData({
      date: selectedDate.value,
      config: selectedMachine.value,
      file: selectedFile.value,
    })

    if (response.code === 200 && response.data) {
      const gpuMemoryData = response.data
      // 使用新的数据结构
      if (gpuMemoryData.x_axis && gpuMemoryData.series) {
        const chartLabels = gpuMemoryData.x_axis
        const chartSizes = gpuMemoryData.series.y_axis
        const totalMemory = gpuMemoryData.total
        // 更新图表
        updateChart(chartLabels, chartSizes, totalMemory)
        // ElMessage.success('数据加载完成')
      } else {
        ElMessage.error('数据格式不正确')
      }
    } else {
      ElMessage.error('未获取到有效的GPU内存数据')
    }
  } catch (error) {
    console.error('Failed to fetch GPU memory data:', error)
    ElMessage.error('获取GPU内存数据失败: ' + (error.message || '未知错误'))
  } finally {
    chartLoading.value = false // 结束加载
  }
}

const updateChart = (labels, sizes, totalMemory) => {
  if (!chartContainer.value || !labels.length) return
  if (chartInstance) {
    chartInstance.dispose()
  }
  chartInstance = echarts.init(chartContainer.value)
  const option = {
    title: {
      text: `GPU内存分析 - 总计: ${totalMemory.toFixed(2)} MB`,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
      },
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: function (params) {
        const data = params[0]
        return `${data.name}<br/>${data.seriesName}: ${data.value.toFixed(2)} MB<br/><span style="color: #409EFF; font-size: 12px;">💡 点击查看详细信息</span>`
      },
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#409EFF',
      borderWidth: 1,
      textStyle: {
        color: '#333',
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: labels,
      axisLabel: {
        rotate: 0, // 不旋转，保持水平显示
        interval: 'auto', // 自动计算间隔，避免标签重叠
        fontSize: 12, // 稍微减小字体大小
        color: '#000000',
        // 添加文本省略功能
        formatter: function (value) {
          // 如果文本长度超过12个字符，进行省略
          if (value.length > 12) {
            return value.substring(0, 12) + '...'
          }
          return value
        },
        // 设置最大宽度，超出部分会被省略
        width: 100,
        overflow: 'truncate',
        // 添加tooltip显示完整文本
        showMaxLabel: true,
        showMinLabel: true,
        // 增加标签的内边距，扩大点击区域
        padding: [5, 8],
        // 添加背景色，在悬停时显示
        backgroundColor: 'transparent',
      },
      // 轴线样式
      axisLine: {
        lineStyle: {
          color: '#333',
        },
      },
      axisTick: {
        alignWithLabel: true,
        // 增加刻度线长度，提供更好的视觉引导
        length: 8,
      },
      // 启用轴标签的触发区域
      triggerEvent: true,
    },
    yAxis: {
      type: 'value',
      name: '内存使用量 (MB)',
      nameTextStyle: {
        fontSize: 12,
      },
      axisLabel: {
        formatter: '{value} MB',
      },
    },
    series: [
      {
        name: '显存占用',
        type: 'bar',
        // 设置最小柱高，确保即使数值很小也有足够的点击区域
        barMinHeight: 20,
        // 设置柱子宽度，提供更好的点击体验
        barMaxWidth: 60,
        data: sizes.map((value, index) => ({
          value: value,
          name: labels[index],
          itemStyle: {
            color: generateColor(index),
            // 添加边框，增强视觉效果
            borderColor: '#fff',
            borderWidth: 1,
          },
        })),
        label: {
          show: true,
          position: 'top',
          formatter: function (params) {
            return `${params.value.toFixed(2)} MB`
          },
          fontSize: 12,
          // color: '#000000',
          // fontWeight: 'bold'
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
            // 悬停时增加透明度变化
            opacity: 0.8,
          },
        },
        // 添加选中状态样式
        select: {
          itemStyle: {
            borderColor: '#409EFF',
            borderWidth: 3,
            shadowBlur: 15,
            shadowColor: 'rgba(64, 158, 255, 0.3)',
          },
        },
      },
    ],
  }

  chartInstance.setOption(option)

  // 添加图表点击事件 - 优化小柱状图的点击体验
  chartInstance.on('click', function (params) {
    if (params.componentType === 'series') {
      const categoryName = params.name

      // 判断点击的是否与当前选中的分类相同，如果相同则不发起请求
      if (selectedCategory.value === categoryName) {
        return
      }

      // 更新选中状态的视觉效果
      updateSelectedBarStyle(categoryName, labels)
      selectedCategory.value = categoryName
      getTableData(categoryName, 1, pageSize.value)
    }
  })

  // 添加X轴标签点击事件，提供额外的点击方式
  chartInstance.on('click', function (params) {
    if (params.componentType === 'xAxis') {
      const categoryName = params.value

      // 判断点击的是否与当前选中的分类相同，如果相同则不发起请求
      if (selectedCategory.value === categoryName) {
        return
      }

      // 更新选中状态的视觉效果
      updateSelectedBarStyle(categoryName, labels)
      selectedCategory.value = categoryName
      getTableData(categoryName, 1, pageSize.value)
    }
  })

  // 添加鼠标悬停效果，提供视觉反馈
  chartInstance.on('mouseover', function (params) {
    if (params.componentType === 'series') {
      // 改变鼠标样式
      chartContainer.value.style.cursor = 'pointer'
    }
  })

  chartInstance.on('mouseout', function (params) {
    if (params.componentType === 'series') {
      // 恢复默认鼠标样式
      chartContainer.value.style.cursor = 'default'
    }
  })

  // 监听窗口大小变化
  const resizeHandler = () => {
    if (chartInstance) {
      chartInstance.resize()
    }
  }
  window.addEventListener('resize', resizeHandler)

  // 添加键盘导航支持
  const keyboardHandler = (event) => {
    if (!labels || labels.length === 0) return

    const currentIndex = labels.indexOf(selectedCategory.value)
    let newIndex = currentIndex

    switch (event.key) {
      case 'ArrowLeft':
        event.preventDefault()
        newIndex = currentIndex > 0 ? currentIndex - 1 : labels.length - 1
        break
      case 'ArrowRight':
        event.preventDefault()
        newIndex = currentIndex < labels.length - 1 ? currentIndex + 1 : 0
        break
      default:
        return
    }

    if (newIndex !== currentIndex) {
      const newCategory = labels[newIndex]
      updateSelectedBarStyle(newCategory, labels)
      selectedCategory.value = newCategory
      getTableData(newCategory, 1, pageSize.value)
    }
  }

  // 当图表容器获得焦点时启用键盘导航
  if (chartContainer.value) {
    chartContainer.value.setAttribute('tabindex', '0')
    chartContainer.value.addEventListener('keydown', keyboardHandler)
  }

  chartData.value = { labels, data: sizes }

  // 柱状图加载完成后，自动选择第一个分类作为表格的默认数据
  if (labels.length > 0) {
    const firstCategory = labels[0]
    selectedCategory.value = firstCategory
    // 设置第一个柱状图为选中状态
    updateSelectedBarStyle(firstCategory, labels)
    getTableData(firstCategory, 1, pageSize.value)
  }
}

// 更新选中柱状图的样式
const updateSelectedBarStyle = (selectedName, allLabels) => {
  if (!chartInstance) return

  const option = chartInstance.getOption()
  const seriesData = option.series[0].data

  // 更新所有柱状图的样式
  const updatedData = seriesData.map((item, index) => {
    const isSelected = item.name === selectedName
    return {
      ...item,
      itemStyle: {
        color: generateColor(index),
        borderColor: isSelected ? '#409EFF' : '#fff',
        borderWidth: isSelected ? 3 : 1,
        shadowBlur: isSelected ? 15 : 0,
        shadowColor: isSelected ? 'rgba(64, 158, 255, 0.3)' : 'transparent',
      },
    }
  })

  // 更新图表
  chartInstance.setOption({
    series: [
      {
        data: updatedData,
      },
    ],
  })
}

// 获取表格数据的方法
const getTableData = async (name, page = 1, page_size = 10, keywords = '') => {
  try {
    if (!selectedDate.value || !selectedMachine.value || !selectedFile.value) {
      ElMessage.warning('请先选择日期、机型和文件')
      return
    }

    tableLoading.value = true
    currentPage.value = page
    currentTableTitle.value = `${name}分类_资源详细信息`

    // 构建API参数
    const params = {
      date: selectedDate.value,
      config: selectedMachine.value,
      file: selectedFile.value,
      name: name, // 分类参数
      page: page, // 分页参数
      page_size: page_size, // 分页参数
    }

    // 如果有关键字，添加到参数中
    if (keywords && keywords.trim()) {
      params.keywords = keywords.trim()
    }

    // 调用getGPUMemoryDetail API
    const response = await getGPUMemoryDetail(params)
    // console.log('response', response)
    if (response.code === 200 && response.data) {
      tableData.value = response.data?.records || []
      totalCount.value = response.data.total || 0
      // currentTableTitle.value = `${name}分类_资源详细信息`
      // ElMessage.success(`已加载 ${name} 的详细数据`)
    } else {
      ElMessage.error('获取表格数据失败')
      tableData.value = []
      totalCount.value = 0
    }
  } catch (error) {
    console.error('Failed to fetch table data:', error)
    ElMessage.error('获取表格数据失败: ' + (error.message || '未知错误'))
    tableData.value = []
    totalCount.value = 0
  } finally {
    tableLoading.value = false
  }
}

// 搜索输入框失去焦点事件处理
const handleSearchBlur = () => {
  if (selectedCategory.value && searchKeywords.value) {
    // 重置到第一页并使用当前搜索关键字进行搜索
    getTableData(selectedCategory.value, 1, pageSize.value, searchKeywords.value)
  }
}

// 搜索输入框清除事件处理
const handleSearchClear = () => {
  if (selectedCategory.value) {
    // 清除搜索关键字后重新请求数据
    getTableData(selectedCategory.value, 1, pageSize.value, '')
  }
}

// 分页变化处理
const handlePageChange = (page) => {
  if (selectedCategory.value) {
    getTableData(selectedCategory.value, page, pageSize.value, searchKeywords.value)
  }
}

// 每页大小变化处理
const handleSizeChange = (size) => {
  pageSize.value = size
  if (selectedCategory.value) {
    getTableData(selectedCategory.value, 1, size, searchKeywords.value)
  }
}

// 生成颜色的函数
const generateColor = (index) => {
  const colors = [
    'rgba(54, 162, 235, 0.6)',
    'rgba(255, 99, 132, 0.6)',
    'rgba(75, 192, 192, 0.6)',
    'rgba(255, 206, 86, 0.6)',
    'rgba(153, 102, 255, 0.6)',
    'rgba(255, 159, 64, 0.6)',
    'rgba(199, 199, 199, 0.6)',
    'rgba(83, 202, 115, 0.6)',
    'rgba(192, 192, 192, 0.6)',
    'rgba(155, 89, 182, 0.6)',
    'rgba(255, 215, 0, 0.6)',
    'rgba(144, 238, 144, 0.6)',
    'rgba(255, 127, 80, 0.6)',
    'rgba(189, 189, 189, 0.6)',
    'rgba(242, 242, 242, 0.6)',
    'rgba(127, 127, 127, 0.6)',
    'rgba(0, 0, 0, 0.6)',
    'rgba(255, 255, 255, 0.6)',
  ]
  return colors[index % colors.length]
}

// 组件挂载时初始化
onMounted(async () => {
  await loadAvailableDates()
  // 确保在DOM渲染完成后初始化图表
  // await nextTick()
  // await fetchData()
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  // 移除事件监听器
  window.removeEventListener('resize', () => {
    if (chartInstance) {
      chartInstance.resize()
    }
  })

  // 移除键盘事件监听器
  if (chartContainer.value) {
    chartContainer.value.removeEventListener('keydown', () => {})
  }
})
</script>

<style scoped lang="scss">
.gpu-memory-page {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;

  .header-left {
    display: flex;
    align-items: center;
    gap: 15px;

    .back-button {
      margin-right: 5px;
    }

    h1 {
      margin: 0;
      color: #333;
      font-size: 24px;
    }
  }

  .header-controls {
    display: flex;
    gap: 24px;
    flex-wrap: wrap;
    align-items: center;

    .control-group {
      display: flex;
      align-items: center;
      gap: 12px;
      min-width: auto;

      label {
        font-weight: 500;
        color: #666;
        font-size: 14px;
        white-space: nowrap;
        margin: 0;
      }

      .el-select {
        min-width: 160px;
      }
    }
  }
}

.chart-section {
  margin-bottom: 15px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .chart-header {
    margin-bottom: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .chart-tip {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #909399;
      font-size: 14px;
      background: #f4f4f5;
      padding: 8px 12px;
      border-radius: 4px;
      border-left: 3px solid #409eff;

      .el-icon {
        color: #409eff;
      }
    }
  }

  .chart-container {
    width: 100%;
    height: 600px;
    // 添加一些视觉提示，表明这是可交互的
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      pointer-events: none;
      border: 2px dashed transparent;
      border-radius: 4px;
      transition: border-color 0.3s ease;
    }

    &:hover::after {
      border-color: rgba(64, 158, 255, 0.3);
    }
  }

  .empty-chart {
    width: 100%;
    height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.data-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;

  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    h3 {
      margin: 0;
      color: #333;
      font-size: 18px;
      font-weight: 600;
    }

    .table-info {
      color: #666;
      font-size: 14px;
    }

    .table-controls {
      display: flex;
      gap: 15px;
      align-items: center;

      .search-input {
        width: 250px;
      }

      .table-info {
        color: #666;
        font-size: 14px;
        white-space: nowrap;
      }
    }
  }

  .table-stats {
    margin-bottom: 15px;
    color: #666;
    font-size: 14px;
  }

  .pagination-wrapper {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }

  :deep(.heap-row) {
    background-color: #f0f9ff;
    cursor: pointer;

    &:hover {
      background-color: #e0f2fe;
    }
  }

  :deep(.el-table__row) {
    &:hover {
      background-color: #f5f5f5;
    }
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;

    .header-left {
      width: 100%;
      justify-content: flex-start;
    }

    .header-controls {
      width: 100%;
      flex-direction: column;
      gap: 15px;
      align-items: stretch;

      .control-group {
        min-width: auto;
        flex-direction: row;
        justify-content: space-between;

        label {
          flex-shrink: 0;
        }

        .el-select {
          flex: 1;
          min-width: 120px;
          max-width: 200px;
        }
      }
    }
  }

  .chart-section {
    .chart-container {
      height: 400px;
    }

    .empty-chart {
      height: 300px;
    }
  }
}
</style>
