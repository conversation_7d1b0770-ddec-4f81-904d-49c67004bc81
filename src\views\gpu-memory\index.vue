<template>
  <div class="gpu-memory-page">
    <div class="page-header">
      <div class="header-left">
        <el-button @click="goBack" type="primary" plain class="back-button">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <h1>GPU内存分析</h1>
      </div>

      <div class="header-controls">
        <div class="control-group">
          <label for="dateSelect">选择日期:</label>
          <el-select
            v-model="selectedDate"
            placeholder="选择日期"
            @change="onDateChange"
            style="width: 200px"
          >
            <el-option v-for="date in availableDates" :key="date" :label="date" :value="date" />
          </el-select>
        </div>

        <div class="control-group">
          <label for="machineSelect">选择机型:</label>
          <el-select
            v-model="selectedMachine"
            placeholder="选择机型"
            @change="onMachineChange"
            style="width: 220px"
            :disabled="!selectedDate"
          >
            <el-option
              v-for="machine in availableMachines"
              :key="machine"
              :label="machine.label"
              :value="machine.value"
            />
          </el-select>
        </div>

        <div class="control-group">
          <label for="fileSelect">选择文件:</label>
          <el-select
            v-model="selectedFile"
            placeholder="选择文件"
            @change="onFileChange"
            style="width: 250px"
            :disabled="!selectedMachine"
          >
            <el-option v-for="file in availableFiles" :key="file" :label="file" :value="file" />
          </el-select>
        </div>
      </div>
    </div>

    <div class="chart-section">
      <div
        ref="chartContainer"
        class="chart-container"
        v-loading="chartLoading"
        element-loading-text="数据正在加载中..."
      ></div>
    </div>

    <!-- 点击柱状图后显示的详细表格 -->
    <div class="data-section">
      <div class="table-header">
        <h3>
          {{ currentTableTitle
          }}{{ !tableLoading && totalCount > 0 ? `(共${totalCount}条数据)` : '' }}
        </h3>
        <div class="table-controls">
          <el-input
            v-model="searchKeywords"
            placeholder="输入关键字搜索..."
            class="search-input"
            @blur="handleSearchBlur"
            @clear="handleSearchClear"
            clearable
          />
        </div>
      </div>

      <el-table
        :data="tableData"
        style="width: 100%"
        height="500"
        v-loading="tableLoading"
        element-loading-text="正在加载详细数据..."
        border
      >
        <el-table-column prop="name" label="名称(Name)" width="320" show-overflow-tooltip />
        <el-table-column prop="memory" label="内存(Memory) (MB)" width="180"></el-table-column>
        <el-table-column prop="resolution" label="分辨率(Resolution)" width="180" />
        <el-table-column prop="format" label="格式(Format)" width="170" show-overflow-tooltip />
        <el-table-column prop="type" label="类型(Type)" width="140" />
        <el-table-column prop="owner" label="Owner" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.owner || 'None' }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalCount"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ArrowLeft } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import { useDashboardApi } from '@/api/dashboaard/index.js'
const { getDate, getMemoryFile, getDeviceConfig, getGPUMemoryData, getGPUMemoryDetail } =
  useDashboardApi()
const router = useRouter()
const route = useRoute()

// 响应式数据
const selectedDate = ref('')
const selectedMachine = ref('')
const selectedFile = ref('')
const availableDates = ref([])
const availableMachines = ref([])
const availableFiles = ref([])
const chartData = ref({})
const currentTableData = ref([])
const currentTableTitle = ref('')
const chartContainer = ref(null)

// 新增表格相关的响应式数据
const tableData = ref([])
const tableLoading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
const selectedCategory = ref('')

// 柱状图加载状态
const chartLoading = ref(false)

// 搜索关键字
const searchKeywords = ref('')

let chartInstance = null

// 方法
const goBack = () => {
  // router.go(-1)
  // 跳转回首页
  router.push('/')
}
// 动态加载数据的方法
const loadAvailableDates = async () => {
  try {
    const response = await getDate()
    if (response.code === 200 && response.data) {
      availableDates.value = response.data
    } else {
      availableDates.value = []
      ElMessage.warning('未获取到可用日期数据')
    }

    // 从URL参数中获取默认日期，如果没有则使用第一个可用日期
    const urlDate = route.query.date
    if (availableDates.value.length > 0) {
      if (urlDate && availableDates.value.includes(urlDate)) {
        selectedDate.value = urlDate
      } else {
        selectedDate.value = availableDates.value[0]
      }
      await loadAvailableMachines()
    }
  } catch (error) {
    console.error('Failed to load available dates:', error)
    ElMessage.error('获取可用日期失败')
    availableDates.value = []
  }
}

const loadAvailableMachines = async () => {
  if (!selectedDate.value) return
  try {
    const response = await getDeviceConfig({ date: selectedDate.value })
    if (response.code === 200 && response.data) {
      availableMachines.value = response.data?.configs?.map((config) => ({
        label: config,
        value: config,
      }))
    } else {
      availableMachines.value = []
      ElMessage.warning('未获取到设备配置数据')
    }
    // 从URL参数中获取默认机器配置，如果没有则使用第一个可用配置
    const urlMachine = route.query.machine
    if (availableMachines.value.length > 0) {
      const machineExists = availableMachines.value.some((machine) => machine.value === urlMachine)
      if (urlMachine && machineExists) {
        selectedMachine.value = urlMachine
      } else {
        selectedMachine.value = availableMachines.value[0].value
      }
      await loadAvailableFiles()
    }
  } catch (error) {
    console.error('Failed to load available machines:', error)
    ElMessage.error('获取设备配置失败')
    availableMachines.value = []
  }
}

const loadAvailableFiles = async () => {
  if (!selectedDate.value || !selectedMachine.value) return

  try {
    const response = await getMemoryFile({
      date: selectedDate.value,
      config: selectedMachine.value,
    })
    if (response && response.data) {
      availableFiles.value = response.data
    } else {
      availableFiles.value = []
      ElMessage.warning('未获取到内存文件数据')
    }

    // 设置默认选中文件：优先选择'rhiDumpResourceMemory-start.csv'，否则选择第一个文件
    if (availableFiles.value.length > 0) {
      const preferredFile = 'rhiDumpResourceMemory-start.csv'
      const hasPreferredFile = availableFiles.value.includes(preferredFile)
      selectedFile.value = hasPreferredFile ? preferredFile : availableFiles.value[0]
      await fetchData()
    }
  } catch (error) {
    console.error('Failed to load available files:', error)
    ElMessage.error('获取内存文件失败')
    availableFiles.value = []
  }
}

// 监听下拉选择变化
const onDateChange = async () => {
  selectedMachine.value = ''
  selectedFile.value = ''
  availableMachines.value = []
  availableFiles.value = []
  currentTableData.value = []

  if (selectedDate.value) {
    await loadAvailableMachines()
    updateURL()
  }
}

const onMachineChange = async () => {
  selectedFile.value = ''
  availableFiles.value = []
  currentTableData.value = []

  if (selectedMachine.value) {
    await loadAvailableFiles()
    updateURL()
  }
}

const onFileChange = async () => {
  if (selectedFile.value) {
    await fetchData()
    updateURL()
  }
}

// 更新URL参数
const updateURL = () => {
  const query = {}
  if (selectedDate.value) query.date = selectedDate.value
  if (selectedMachine.value) query.machine = selectedMachine.value
  router.replace({ query })
}

// 数据获取函数 - 使用新的数据结构
const fetchData = async () => {
  try {
    if (!selectedDate.value || !selectedMachine.value || !selectedFile.value) {
      ElMessage.warning('请先选择日期、机型和文件')
      return
    }

    chartLoading.value = true // 开始加载

    // 使用真实API接口获取GPU内存数据
    const response = await getGPUMemoryData({
      date: selectedDate.value,
      config: selectedMachine.value,
      file: selectedFile.value,
    })

    if (response.code === 200 && response.data) {
      const gpuMemoryData = response.data
      // 使用新的数据结构
      if (gpuMemoryData.x_axis && gpuMemoryData.series) {
        const chartLabels = gpuMemoryData.x_axis
        const chartSizes = gpuMemoryData.series.y_axis
        const totalMemory = gpuMemoryData.total
        // 更新图表
        updateChart(chartLabels, chartSizes, totalMemory)
        // ElMessage.success('数据加载完成')
      } else {
        ElMessage.error('数据格式不正确')
      }
    } else {
      ElMessage.error('未获取到有效的GPU内存数据')
    }
  } catch (error) {
    console.error('Failed to fetch GPU memory data:', error)
    ElMessage.error('获取GPU内存数据失败: ' + (error.message || '未知错误'))
  } finally {
    chartLoading.value = false // 结束加载
  }
}

const updateChart = (labels, sizes, totalMemory) => {
  if (!chartContainer.value || !labels.length) return
  if (chartInstance) {
    chartInstance.dispose()
  }
  chartInstance = echarts.init(chartContainer.value)
  const option = {
    title: {
      text: `GPU内存分析 - 总计: ${totalMemory.toFixed(2)} MB`,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
      },
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: function (params) {
        const data = params[0]
        return `${data.name}<br/>${data.seriesName}: ${data.value.toFixed(2)} MB`
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: labels,
      axisLabel: {
        rotate: 0, // 不旋转，保持水平显示
        interval: 'auto', // 自动计算间隔，避免标签重叠
        fontSize: 12, // 稍微减小字体大小
        color: '#000000',
        // 添加文本省略功能
        formatter: function (value) {
          // 如果文本长度超过12个字符，进行省略
          if (value.length > 12) {
            return value.substring(0, 12) + '...'
          }
          return value
        },
        // 设置最大宽度，超出部分会被省略
        width: 100,
        overflow: 'truncate',
        // 添加tooltip显示完整文本
        showMaxLabel: true,
        showMinLabel: true,
      },
      // 轴线样式
      axisLine: {
        lineStyle: {
          color: '#333',
        },
      },
      axisTick: {
        alignWithLabel: true,
      },
    },
    yAxis: {
      type: 'value',
      name: '内存使用量 (MB)',
      nameTextStyle: {
        fontSize: 12,
      },
      axisLabel: {
        formatter: '{value} MB',
      },
    },
    series: [
      {
        name: '显存占用',
        type: 'bar',
        data: sizes.map((value, index) => ({
          value: value,
          name: labels[index],
          itemStyle: {
            color: generateColor(index),
          },
        })),
        label: {
          show: true,
          position: 'top',
          formatter: function (params) {
            return `${params.value.toFixed(2)} MB`
          },
          fontSize: 12,
          // color: '#000000',
          // fontWeight: 'bold'
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  }

  chartInstance.setOption(option)

  // 添加图表点击事件
  chartInstance.on('click', function (params) {
    if (params.componentType === 'series') {
      const categoryName = params.name

      // 判断点击的是否与当前选中的分类相同，如果相同则不发起请求
      if (selectedCategory.value === categoryName) {
        return
      }

      selectedCategory.value = categoryName
      getTableData(categoryName, 1, pageSize.value)
    }
  })

  // 监听窗口大小变化
  const resizeHandler = () => {
    if (chartInstance) {
      chartInstance.resize()
    }
  }
  window.addEventListener('resize', resizeHandler)

  chartData.value = { labels, data: sizes }

  // 柱状图加载完成后，自动选择第一个分类作为表格的默认数据
  if (labels.length > 0) {
    const firstCategory = labels[0]
    selectedCategory.value = firstCategory
    getTableData(firstCategory, 1, pageSize.value)
  }
}

// 获取表格数据的方法
const getTableData = async (name, page = 1, page_size = 10, keywords = '') => {
  try {
    if (!selectedDate.value || !selectedMachine.value || !selectedFile.value) {
      ElMessage.warning('请先选择日期、机型和文件')
      return
    }

    tableLoading.value = true
    currentPage.value = page
    currentTableTitle.value = `${name}分类_资源详细信息`

    // 构建API参数
    const params = {
      date: selectedDate.value,
      config: selectedMachine.value,
      file: selectedFile.value,
      name: name, // 分类参数
      page: page, // 分页参数
      page_size: page_size, // 分页参数
    }

    // 如果有关键字，添加到参数中
    if (keywords && keywords.trim()) {
      params.keywords = keywords.trim()
    }

    // 调用getGPUMemoryDetail API
    const response = await getGPUMemoryDetail(params)
    // console.log('response', response)
    if (response.code === 200 && response.data) {
      tableData.value = response.data?.records || []
      totalCount.value = response.data.total || 0
      // currentTableTitle.value = `${name}分类_资源详细信息`
      // ElMessage.success(`已加载 ${name} 的详细数据`)
    } else {
      ElMessage.error('获取表格数据失败')
      tableData.value = []
      totalCount.value = 0
    }
  } catch (error) {
    console.error('Failed to fetch table data:', error)
    ElMessage.error('获取表格数据失败: ' + (error.message || '未知错误'))
    tableData.value = []
    totalCount.value = 0
  } finally {
    tableLoading.value = false
  }
}

// 搜索输入框失去焦点事件处理
const handleSearchBlur = () => {
  if (selectedCategory.value && searchKeywords.value) {
    // 重置到第一页并使用当前搜索关键字进行搜索
    getTableData(selectedCategory.value, 1, pageSize.value, searchKeywords.value)
  }
}

// 搜索输入框清除事件处理
const handleSearchClear = () => {
  if (selectedCategory.value) {
    // 清除搜索关键字后重新请求数据
    getTableData(selectedCategory.value, 1, pageSize.value, '')
  }
}

// 分页变化处理
const handlePageChange = (page) => {
  if (selectedCategory.value) {
    getTableData(selectedCategory.value, page, pageSize.value, searchKeywords.value)
  }
}

// 每页大小变化处理
const handleSizeChange = (size) => {
  pageSize.value = size
  if (selectedCategory.value) {
    getTableData(selectedCategory.value, 1, size, searchKeywords.value)
  }
}

// 生成颜色的函数
const generateColor = (index) => {
  const colors = [
    'rgba(54, 162, 235, 0.6)',
    'rgba(255, 99, 132, 0.6)',
    'rgba(75, 192, 192, 0.6)',
    'rgba(255, 206, 86, 0.6)',
    'rgba(153, 102, 255, 0.6)',
    'rgba(255, 159, 64, 0.6)',
    'rgba(199, 199, 199, 0.6)',
    'rgba(83, 202, 115, 0.6)',
    'rgba(192, 192, 192, 0.6)',
    'rgba(155, 89, 182, 0.6)',
    'rgba(255, 215, 0, 0.6)',
    'rgba(144, 238, 144, 0.6)',
    'rgba(255, 127, 80, 0.6)',
    'rgba(189, 189, 189, 0.6)',
    'rgba(242, 242, 242, 0.6)',
    'rgba(127, 127, 127, 0.6)',
    'rgba(0, 0, 0, 0.6)',
    'rgba(255, 255, 255, 0.6)',
  ]
  return colors[index % colors.length]
}

// 组件挂载时初始化
onMounted(async () => {
  await loadAvailableDates()
  // 确保在DOM渲染完成后初始化图表
  // await nextTick()
  // await fetchData()
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', () => {
    if (chartInstance) {
      chartInstance.resize()
    }
  })
})
</script>

<style scoped lang="scss">
.gpu-memory-page {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;

  .header-left {
    display: flex;
    align-items: center;
    gap: 15px;

    .back-button {
      margin-right: 5px;
    }

    h1 {
      margin: 0;
      color: #333;
      font-size: 24px;
    }
  }

  .header-controls {
    display: flex;
    gap: 24px;
    flex-wrap: wrap;
    align-items: center;

    .control-group {
      display: flex;
      align-items: center;
      gap: 12px;
      min-width: auto;

      label {
        font-weight: 500;
        color: #666;
        font-size: 14px;
        white-space: nowrap;
        margin: 0;
      }

      .el-select {
        min-width: 160px;
      }
    }
  }
}

.chart-section {
  margin-bottom: 15px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .chart-container {
    width: 100%;
    height: 600px;
  }

  .empty-chart {
    width: 100%;
    height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.data-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;

  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    h3 {
      margin: 0;
      color: #333;
      font-size: 18px;
      font-weight: 600;
    }

    .table-info {
      color: #666;
      font-size: 14px;
    }

    .table-controls {
      display: flex;
      gap: 15px;
      align-items: center;

      .search-input {
        width: 250px;
      }

      .table-info {
        color: #666;
        font-size: 14px;
        white-space: nowrap;
      }
    }
  }

  .table-stats {
    margin-bottom: 15px;
    color: #666;
    font-size: 14px;
  }

  .pagination-wrapper {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }

  :deep(.heap-row) {
    background-color: #f0f9ff;
    cursor: pointer;

    &:hover {
      background-color: #e0f2fe;
    }
  }

  :deep(.el-table__row) {
    &:hover {
      background-color: #f5f5f5;
    }
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;

    .header-left {
      width: 100%;
      justify-content: flex-start;
    }

    .header-controls {
      width: 100%;
      flex-direction: column;
      gap: 15px;
      align-items: stretch;

      .control-group {
        min-width: auto;
        flex-direction: row;
        justify-content: space-between;

        label {
          flex-shrink: 0;
        }

        .el-select {
          flex: 1;
          min-width: 120px;
          max-width: 200px;
        }
      }
    }
  }

  .chart-section {
    .chart-container {
      height: 400px;
    }

    .empty-chart {
      height: 300px;
    }
  }
}
</style>
