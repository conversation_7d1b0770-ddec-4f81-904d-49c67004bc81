<template>
  <div class="dashboard-container">
    <!-- 页面标题 -->
    <h1 class="page-title">ECO - 性能数据分析平台</h1>

    <!-- 控制面板 -->
    <div class="control-panel">
      <!-- 第一行：日期、配置和指标选择 -->
      <div class="control-row">
        <!-- 日期选择 -->
        <div class="control-item" style="width: 20%">
          <label class="control-label">选择日期：</label>
          <el-select
            v-model="selectedDate"
            placeholder="请选择日期"
            @change="onDateChange"
            style="width: 100%"
          >
            <el-option
              v-for="date in availableDates"
              :key="date.value"
              :label="date.label"
              :value="date.value"
            />
          </el-select>
        </div>

        <!-- 配置选择 -->
        <div class="control-item" style="width: 25%">
          <label class="control-label">选择机器配置：</label>
          <el-select
            v-model="selectedConfigs"
            multiple
            placeholder="请选择配置..."
            @change="onConfigChange"
            style="width: 100%"
          >
            <el-option
              v-for="config in availableConfigs"
              :key="config.value"
              :label="config.label"
              :value="config.value"
            />
          </el-select>
        </div>

        <!-- 指标选择 -->
        <div class="control-item" style="width: 20%">
          <label class="control-label">选择性能指标：</label>
          <el-select
            v-model="selectedMetric"
            placeholder="请选择指标..."
            @change="onMetricChange"
            filterable
            style="width: 100%"
          >
            <el-option
              v-for="metric in availableMetrics"
              :key="metric.value"
              :label="metric.label"
              :value="metric.value"
            />
          </el-select>
        </div>

        <!-- GPU显存按钮 -->
        <div class="control-item" style="width: 10%">
          <el-button
            type="primary"
            text
            @click="goToGPUMemoryPage"
            style="width: 100%; height: 38px; font-size: 14px"
          >
            查看GPU显存
          </el-button>
        </div>
      </div>

      <!-- 第二行：对比日期选择 -->
      <div class="control-row">
        <div class="control-item" style="width: 25%">
          <label class="control-label">对比日期：</label>
          <el-select
            v-model="compareDate"
            placeholder="选择对比日期"
            clearable
            :disabled="!enableCompare"
            style="width: 100%"
          >
            <el-option
              v-for="date in availableDates"
              :key="date.value"
              :label="date.label"
              :value="date.value"
              :disabled="date.value === selectedDate"
            />
          </el-select>
          <el-checkbox v-model="enableCompare" style="margin-left: 10px"> 启用对比 </el-checkbox>
        </div>
      </div>

      <!-- 第三行：字段选择 -->
      <div class="control-row">
        <div class="control-item" style="width: 100%">
          <label class="control-label">选择字段：</label>
          <!-- 字段选择头部：包含全选/清空按钮 -->
          <div class="field-header">
            <span class="field-title"
              >字段选择 ({{ selectedFields.length }}/{{ availableFields.length }})</span
            >
            <div class="field-actions">
              <el-button size="small" @click="selectAllFields" style="margin-right: 10px">
                全选
              </el-button>
              <el-button size="small" @click="clearAllFields"> 清空 </el-button>
            </div>
          </div>
          <div class="field-toggle-container">
            <div class="field-grid">
              <el-checkbox-group v-model="selectedFields">
                <el-checkbox
                  v-for="field in availableFields"
                  :key="field"
                  :value="field"
                  class="field-checkbox"
                  :title="field"
                >
                  {{ field }}
                </el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
        </div>
      </div>

      <!-- 更新按钮 -->
      <div class="update-section">
        <el-button type="primary" size="large" @click="updateChart" :loading="loading">
          更新图表
        </el-button>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="chart-section">
      <div ref="chartContainer" class="chart-container"></div>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <div class="table-header">
        <h4 style="font-weight: bold; color: #333">性能指标统计</h4>
        <el-input
          v-model="searchKeywords"
          placeholder="输入关键字搜索..."
          clearable
          style="width: 300px"
          @blur="onSearchKeywords"
          @clear="onClearSearch"
        />
      </div>
      <el-table
        :data="tableData"
        style="width: 100%"
        v-loading="loading"
        height="500"
        :page-size="30"
        stripe
        border
        @sort-change="handleSortChange"
      >
        <el-table-column prop="config" label="配置" sortable="custom" show-overflow-tooltip />
        <el-table-column prop="field" label="字段" sortable="custom" show-overflow-tooltip />
        <el-table-column prop="mean" label="平均值" sortable="custom"> </el-table-column>
        <el-table-column prop="min" label="最小值" sortable="custom"> </el-table-column>
        <el-table-column prop="max" label="最大值" sortable="custom"> </el-table-column>
        <el-table-column prop="std" label="标准差" sortable="custom"> </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup name="Dashboard">
import { ref, onMounted, nextTick, watch } from 'vue'
import { useRouter } from 'vue-router'
import * as echarts from 'echarts'
import { ElMessage } from 'element-plus'
import { useDashboardApi } from '../../api/dashboaard/index.js'
const {
  getDate,
  getDeviceConfig,
  getAvailableMetrics,
  getAvailableFields,
  getChartData,
  getStatsData,
} = useDashboardApi()
const router = useRouter()

// 选择的数据
const selectedDate = ref('')
const selectedConfigs = ref([])
const selectedMetric = ref('')
const compareDate = ref('')
const enableCompare = ref(false)
const selectedFields = ref([])
const loading = ref(false)

// 可选项数据
const availableDates = ref([])
const availableConfigs = ref([])
const availableMetrics = ref([])
const availableFields = ref([])

// 图表和表格数据
const chartContainer = ref(null)
const tableData = ref([])
let chartInstance = null
// 当前性能数据
const currentPerformanceData = ref({})

// 分页相关数据
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 搜索关键字
const searchKeywords = ref('')

// 排序状态
const sortState = ref({
  config_sort: '',
  field_sort: '',
  mean_sort: '',
  min_sort: '',
  max_sort: '',
  std_sort: '',
})

// 初始化数据
const initializeData = async () => {
  try {
    loading.value = true
    // 获取可用日期 - 使用真实API
    const dateResponse = await getDate()
    if (dateResponse && dateResponse.data) {
      availableDates.value = dateResponse.data.map((date) => ({
        label: date,
        value: date,
      }))
    }

    // 设置默认值
    if (availableDates.value.length > 0) {
      selectedDate.value = availableDates.value[0].value
      await loadConfigsForDate(selectedDate.value)
    }

    // 在有日期和配置后，加载指标
    if (selectedDate.value && selectedConfigs.value.length > 0) {
      await loadMetricsForDateAndConfigs()
    }
  } catch (error) {
    ElMessage.error('初始化数据失败!')
  } finally {
    loading.value = false
  }
}

// 加载指定日期的配置 - 使用真实API
const loadConfigsForDate = async (date) => {
  try {
    const response = await getDeviceConfig({ date })
    if (response && response.data) {
      availableConfigs.value = response.data?.configs?.map((config) => ({
        label: config,
        value: config,
      }))
    }

    // 清空之前的配置选择
    selectedConfigs.value = []

    // 默认选择第一个配置
    if (availableConfigs.value.length > 0) {
      selectedConfigs.value = [availableConfigs.value[0].value]
    }

    // 配置加载完成后，重新加载指标
    if (date && selectedConfigs.value.length > 0) {
      await loadMetricsForDateAndConfigs()
    }
  } catch (error) {
    ElMessage.error('加载配置失败: ' + error.message)
  }
}

// 加载指定日期和配置的可用指标 - 使用真实API
const loadMetricsForDateAndConfigs = async () => {
  if (!selectedDate.value || selectedConfigs.value.length === 0) {
    return
  }

  try {
    const response = await getAvailableMetrics({
      date: selectedDate.value,
      configs: selectedConfigs.value.join(','),
    })

    if (response && response.data) {
      availableMetrics.value = response.data.map((metric) => ({
        label: metric,
        value: metric,
      }))
    }

    // 设置默认指标
    if (availableMetrics.value.length > 0 && !selectedMetric.value) {
      // 优先选择'GPU'指标，如果不存在则使用第一个可用指标
      const gpuMetric = availableMetrics.value.find((metric) => metric.value === 'GPU')
      selectedMetric.value = gpuMetric ? gpuMetric.value : availableMetrics.value[0].value
    }
  } catch (error) {
    ElMessage.error('加载指标失败: ' + error.message)
    availableMetrics.value = []
  }
}

// 加载指定指标的字段
const loadFieldsForMetric = async () => {
  if (!selectedDate.value || selectedConfigs.value.length === 0 || !selectedMetric.value) {
    return
  }

  try {
    // 获取字段列表
    const response = await getAvailableFields({
      date: selectedDate.value,
      configs: selectedConfigs.value.join(','), // 多个配置用逗号隔开
      metric: selectedMetric.value,
    })

    if (response && response.data) {
      availableFields.value = response.data
    }
  } catch (error) {
    ElMessage.error('加载字段失败: ' + error.message)
    availableFields.value = []
  }
}

// 清空数据的通用方法
const clearAllData = () => {
  // 清空之前选择的字段
  selectedFields.value = []

  // 清空图表数据
  currentPerformanceData.value = {}

  // 清空数据表格数据
  tableData.value = []
  total.value = 0
  currentPage.value = 1

  // 重置图表显示为初始状态
  if (chartInstance) {
    const option = {
      title: {
        text: '请选择数据进行分析',
        left: 'center',
        top: 'middle',
        textStyle: {
          color: '#999',
          fontSize: 16,
        },
      },
    }
    chartInstance.setOption(option, true)
  }
}

// 事件处理函数
const onDateChange = async (value) => {
  // console.log('Date changed:', value)

  // 调用清空数据方法
  clearAllData()

  await loadConfigsForDate(value)
  // 重新加载字段
  if (selectedMetric.value) {
    await loadFieldsForMetric()
  }
}

const onConfigChange = async (value) => {
  // console.log('Config changed:', value)

  // 调用清空数据方法
  clearAllData()

  // 配置变化时重新加载指标
  await loadMetricsForDateAndConfigs()
  // 重新加载字段
  if (selectedMetric.value) {
    await loadFieldsForMetric()
  }
}

const onMetricChange = async (value) => {
  // console.log('Metric changed:', value)

  // 调用清空数据方法
  clearAllData()

  await loadFieldsForMetric()
}

const goToGPUMemoryPage = () => {
  // 构建查询参数，传递当前选择的日期和机器配置
  const query = {}

  if (selectedDate.value) {
    query.date = selectedDate.value
  }

  if (selectedConfigs.value && selectedConfigs.value.length > 0) {
    // 如果选择了多个配置，传递第一个配置作为默认值
    query.machine = selectedConfigs.value[0]
  }

  router.push({
    path: '/gpu-memory',
    query: query,
  })
}

// 全选字段
const selectAllFields = () => {
  selectedFields.value = [...availableFields.value]
  ElMessage.success('已全选所有字段')
}

// 清空字段选择
const clearAllFields = () => {
  selectedFields.value = []
  ElMessage.success('已清空字段选择')
  clearAllData()
}

const updateChart = async () => {
  if (!selectedDate.value || selectedConfigs.value.length === 0 || !selectedMetric.value) {
    ElMessage.warning('请选择日期、配置和指标')
    return
  }

  if (selectedFields.value.length === 0) {
    ElMessage.warning('请至少选择一个字段')
    return
  }

  // 验证对比功能设置
  if (enableCompare.value && !compareDate.value) {
    ElMessage.warning('当前已开启对比功能，请选择对比日期！')
    return
  }

  loading.value = true

  try {
    // 准备API参数
    const apiParams = {
      date: selectedDate.value,
      configs: selectedConfigs.value,
      metric: selectedMetric.value,
      fields: selectedFields.value,
      is_compare: enableCompare.value && compareDate.value !== null,
      ...(enableCompare.value && compareDate.value && { compare_date: compareDate.value }),
    }
    // 获取图表数据
    const chartResponse = await getChartData(apiParams)
    // 处理图表数据
    if (chartResponse.code === 200) {
      // 验证返回数据的结构
      if (typeof chartResponse.data === 'object' && Object.keys(chartResponse.data).length > 0) {
        currentPerformanceData.value = chartResponse.data
        // 更新图表
        updateChartDisplay()
        ElMessage.success('图表更新成功')
        // 图表更新成功后，调用独立的表格更新方法
        await updateTable()
      } else {
        console.warn('图表API返回的数据为空或格式不正确')
        ElMessage.warning('图表数据为空')
      }
    } else {
      // 使用模拟数据进行测试
      currentPerformanceData.value = {}
      updateChartDisplay()
    }
  } finally {
    loading.value = false
  }
}

// 更新表格数据 - 独立的表格数据获取方法
const updateTable = async (options = {}) => {
  if (!selectedDate.value || selectedConfigs.value.length === 0 || !selectedMetric.value) {
    ElMessage.warning('请选择日期、配置和指标')
    return false
  }

  if (selectedFields.value.length === 0) {
    ElMessage.warning('请至少选择一个字段')
    return false
  }

  try {
    // 准备API参数
    const apiParams = {
      date: selectedDate.value,
      configs: selectedConfigs.value,
      metric: selectedMetric.value,
      fields: selectedFields.value,
      is_compare: enableCompare.value && compareDate.value !== null,
      ...(enableCompare.value && compareDate.value && { compare_date: compareDate.value }),
      // 添加关键字搜索参数
      ...(searchKeywords.value && { keywords: searchKeywords.value }),
      // 添加排序参数
      ...Object.fromEntries(Object.entries(sortState.value).filter(([key, value]) => value !== '')),
      page: currentPage.value,
      page_size: pageSize.value,
    }
    // 获取统计数据
    const statsResponse = await getStatsData(apiParams)
    // 处理统计数据
    if (statsResponse.code == 200 && statsResponse.data?.records) {
      tableData.value = statsResponse.data?.records || []
      total.value = statsResponse.data?.total || 0
      ElMessage.success('表格更新成功')
      return true
    } else {
      console.warn('统计数据API返回为空，使用空数组')
      tableData.value = []
      total.value = 0
      ElMessage.warning('未获取到表格数据')
      return false
    }
  } catch (error) {
    console.error('表格数据加载失败:', error)
    ElMessage.error(error.message || '表格数据加载失败，请重试')

    // 清空表格数据
    tableData.value = []
    total.value = 0
    return false
  }
}

// 分页事件处理函数
const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1 // 重置到第一页
  updateTable()
}

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
  updateTable()
}

// 搜索事件处理函数
const onSearchKeywords = () => {
  // 失去焦点时触发搜索
  currentPage.value = 1 // 重置到第一页
  updateTable()
} // 清空搜索
const onClearSearch = () => {
  searchKeywords.value = ''
  currentPage.value = 1
  updateTable()
}

// 处理表格排序
const handleSortChange = ({ column, prop, order }) => {
  // 重置所有排序状态
  Object.keys(sortState.value).forEach((key) => {
    sortState.value[key] = ''
  })

  // 设置当前排序
  if (prop && order) {
    const sortKey = `${prop}_sort`
    if (sortState.value.hasOwnProperty(sortKey)) {
      sortState.value[sortKey] = order === 'ascending' ? 'asc' : 'desc'
    }
  }

  // 重置到第一页并更新表格
  currentPage.value = 1
  updateTable()
}

// 更新图表显示
const updateChartDisplay = () => {
  if (!chartInstance || Object.keys(currentPerformanceData.value).length === 0) return

  // 检查新数据结构
  const { series = [], x_axis = {}, title = '' } = currentPerformanceData.value

  // 生成图表数据 - 支持新的数据结构
  const seriesData = []
  const legendData = []

  // 定义颜色映射，确保同一字段使用相同颜色
  const fieldColorMap = {}
  // const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc']
  const colors = [
    '#636EFA',
    '#EF553B',
    '#00CC96',
    '#AB63FA',
    '#FFA15A',
    '#19D3F3',
    '#FF6692',
    '#B6E880',
    '#FF97FF',
    '#FECB52',
  ]

  let colorIndex = 0

  // 检查是否包含内存相关字段
  // const hasMemoryField = series.some(
  //   (seriesItem) => seriesItem.name && seriesItem.name.toLowerCase().includes('memory'),
  // )

  // 收集所有Y轴数值以确定格式化需求
  const allYValues = []
  series.forEach((seriesItem) => {
    const { config = {} } = seriesItem
    Object.entries(config).forEach(([configName, configData]) => {
      const { y_axis = [] } = configData
      // 使用循环方式添加数值，避免展开操作符导致的堆栈溢出
      const validValues = y_axis.filter((val) => typeof val === 'number' && !isNaN(val))
      for (const value of validValues) {
        allYValues.push(value)
      }
    })
  })

  // 确定数值格式化参数
  let formatFactor = 1
  let formatUnit = ''
  let needsFormatting = false

  // 安全地计算最大值，避免堆栈溢出
  let maxValue = 0
  if (allYValues.length > 0) {
    // 使用循环方式计算最大值，避免展开操作符导致的堆栈溢出
    maxValue = allYValues.reduce((max, val) => Math.max(max, val), -Infinity)
  }

  // if (hasMemoryField && allYValues.length > 0 && maxValue >= 100000000) {
  //   // 内存字段：字节转GB
  //   formatFactor = 1024 * 1024 * 1024 // 1GB = 1024*1024*1024 bytes
  //   formatUnit = 'GB'
  //   needsFormatting = true
  // }
  if (allYValues.length > 0 && maxValue >= 100000000) {
    formatFactor = 1000000000
    formatUnit = 'B'
    needsFormatting = true
  }
  //  else if (allYValues.length > 0) {
  //   const maxValue = Math.max(...allYValues)
  //   if (maxValue >= 1000000000) {
  //     // 十亿级别：转换为B
  //     formatFactor = 1000000000
  //     formatUnit = 'B'
  //     needsFormatting = true
  //   } else if (maxValue >= 1000000) {
  //     // 百万级别：转换为M
  //     formatFactor = 1000000
  //     formatUnit = 'M'
  //     needsFormatting = true
  //   } else if (maxValue >= 1000) {
  //     // 千级别：转换为K
  //     formatFactor = 1000
  //     formatUnit = 'K'
  //     needsFormatting = true
  //   }
  // }

  // 遍历series数组
  series.forEach((seriesItem) => {
    const { name: fieldName, config = {} } = seriesItem

    // 为每个字段分配固定颜色
    if (!fieldColorMap[fieldName]) {
      fieldColorMap[fieldName] = colors[colorIndex % colors.length]
      colorIndex++
    }

    // 遍历每个配置
    Object.entries(config).forEach(([configName, configData]) => {
      const { y_axis = [], line_type = 'solid' } = configData

      // 获取对应配置的x轴数据
      const xAxisData = x_axis[configName] || []

      if (y_axis.length > 0 && xAxisData.length > 0) {
        const seriesName = `${fieldName} (${configName})`
        legendData.push(seriesName)

        // 应用数值格式化
        const formattedYAxis = needsFormatting
          ? y_axis.map((value) => value / formatFactor)
          : y_axis

        // 将x轴和y轴数据组合成时间序列格式 [时间, 值]
        const data = formattedYAxis.map((value, index) => [
          xAxisData[index] || index * 0.1, // 使用实际的x轴数据，如果没有则回退到默认间隔
          value,
        ])

        // 扩展线条类型配置
        const getLineStyleConfig = (lineType) => {
          const baseConfig = {
            width: 1.5,
            color: fieldColorMap[fieldName],
          }

          switch (lineType) {
            case 'dashed':
              return { ...baseConfig, type: 'dashed' }
            case 'dotted':
              return { ...baseConfig, type: 'dotted' }
            case 'solid':
            default:
              return { ...baseConfig, type: 'solid' }
          }
        }

        seriesData.push({
          name: seriesName,
          type: 'line',
          data: data,
          smooth: true,
          symbol: 'none', // 移除数据点圆圈
          color: fieldColorMap[fieldName], // 使用字段对应的固定颜色
          lineType: line_type, // 添加自定义属性存储线条类型
          lineStyle: getLineStyleConfig(line_type),
          emphasis: {
            focus: 'series',
            lineStyle: {
              width: 2, // 鼠标悬停时稍微加粗
            },
          },
        })
      }
    })
  })

  // 计算Y轴范围
  const formattedYValues = needsFormatting
    ? allYValues.map((value) => value / formatFactor)
    : allYValues

  let yAxisMin = 0
  let yAxisMax = 'dataMax'
  let minInterval = 0.1

  if (formattedYValues.length > 0) {
    // 使用安全的方式计算最小值和最大值，避免展开操作符导致的堆栈溢出
    const minValue = formattedYValues.reduce((min, val) => Math.min(min, val), Infinity)
    const maxValue = formattedYValues.reduce((max, val) => Math.max(max, val), -Infinity)
    const range = maxValue - minValue

    // 添加5%的缓冲区
    const padding = range * 0.05
    yAxisMin = Math.max(0, minValue - padding)
    yAxisMax = maxValue + padding

    // 动态设置最小间隔
    minInterval = range / 20
  }

  const option = {
    title: {
      text: title || `${selectedMetric.value} - 性能指标对比`, // 使用数据中的title，如果没有则使用默认标题
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
      },
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        lineStyle: {
          color: '#999',
          width: 1,
          type: 'dashed',
        },
      },
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#ddd',
      borderWidth: 1,
      borderRadius: 6,
      textStyle: {
        color: '#333',
        fontSize: 12,
      },
      formatter: function (params) {
        let result = `<div style="font-weight: bold; margin-bottom: 5px;">时间: ${params[0].data[0].toFixed(3)}秒</div>`
        params.forEach((param) => {
          // 从series配置中获取线条类型
          const series = seriesData.find((s) => s.name === param.seriesName)
          const lineType = series ? series.lineType : 'solid'

          // 根据线条类型创建更清晰的图标
          let iconHtml = ''
          const iconStyle =
            'display: inline-block; width: 24px; height: 3px; margin-right: 8px; vertical-align: middle; border-radius: 1px;'

          switch (lineType) {
            case 'dashed':
              iconHtml = `<span style="${iconStyle} background: repeating-linear-gradient(to right, ${param.color} 0px, ${param.color} 6px, transparent 6px, transparent 10px); background-size: 20px 100%;"></span>`
              break
            case 'dotted':
              iconHtml = `<span style="${iconStyle} background: repeating-linear-gradient(to right, ${param.color} 0px, ${param.color} 2px, transparent 2px, transparent 5px); background-size: 7px 100%;"></span>`
              break
            case 'solid':
            default:
              iconHtml = `<span style="${iconStyle} background-color: ${param.color};"></span>`
              break
          }

          // 显示格式化后的数值和单位
          const displayValue = needsFormatting
            ? `${param.data[1].toFixed(4)} ${formatUnit}`
            : param.data[1].toFixed(3)

          result += `<div style="margin: 3px 0; display: flex; align-items: center;">${iconHtml}<span>${param.seriesName}: ${displayValue}</span></div>`
        })
        return result
      },
    },
    legend: {
      data: legendData.map((name) => {
        // 从seriesData中找到对应的系列数据
        const series = seriesData.find((s) => s.name === name)
        const lineType = series ? series.lineType : 'solid'

        // 为不同线条类型创建更清晰的SVG图标
        const getIconPath = (type) => {
          // const baseWidth = 30
          const baseHeight = 4
          const y = baseHeight / 2

          switch (type) {
            case 'dashed':
              // 虚线：多个短线段
              return 'path://M5,3 L15,3 L15,7 L5,7 Z M25,3 L35,3 L35,7 L25,7 Z M45,3 L55,3 L55,7 L45,7 Z M65,3 L75,3 L75,7 L65,7 Z M85,3 L95,3 L95,7 L85,7 Z'
            case 'dotted':
              // 点线：多个圆点
              return `path://M4,${y} A1,1 0 1,1 4,${y - 0.1} M10,${y} A1,1 0 1,1 10,${y - 0.1} M16,${y} A1,1 0 1,1 16,${y - 0.1} M22,${y} A1,1 0 1,1 22,${y - 0.1} M28,${y} A1,1 0 1,1 28,${y - 0.1}`
            // case 'dashdot':
            //   // 点划线：长线-点-短线的组合
            //   return `path://M2,${y} L10,${y} M14,${y} A1,1 0 1,1 14,${y - 0.1} M18,${y} L22,${y} M26,${y} L30,${y}`
            // case 'longdash':
            //   // 长虚线：较长的线段
            //   return `path://M2,${y} L14,${y} M18,${y} L30,${y}`
            // case 'shortdash':
            //   // 短虚线：较短的线段
            //   return `path://M2,${y} L6,${y} M10,${y} L14,${y} M18,${y} L22,${y} M26,${y} L30,${y}`
            case 'solid':
            default:
              // 实线：连续线条
              return 'path://M0,0 L100,0 L100,6 L0,6 Z'
          }
        }

        return {
          name: name,
          icon: getIconPath(lineType),
        }
      }),
      top: 40,
      type: 'scroll',
      orient: 'horizontal',
      textStyle: {
        fontSize: 12,
        color: '#666',
      },
      itemGap: 20,
      itemWidth: 32, // 增加图标宽度以适应新的SVG设计
      itemHeight: 16, // 增加图标高度
    },

    grid: {
      left: 60,
      right: 40,
      top: 120, // 增加top值，为legend区域留出更多空间
      bottom: 60,
      containLabel: true,
    },
    xAxis: {
      type: 'value',
      name: '时间 (秒)',
      nameLocation: 'middle',
      nameGap: 30,
      nameTextStyle: {
        color: '#666',
        fontSize: 12,
      },
      axisLabel: {
        formatter: function (value) {
          // 只显示规律间隔的数值，隐藏不规律的数值
          if (value % 20 === 0) {
            return value
          }
          return '' // 不规律的数值返回空字符串，不显示
        },
        color: '#666',
      },
      axisLine: {
        lineStyle: {
          color: '#ddd',
        },
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#f0f0f0',
          type: 'dashed',
        },
      },
      // 设置X轴刻度间隔
      interval: 20,
      // 设置最小值为0
      min: 0,
      // 设置最大值为数据的实际最大值，保持图表完整显示
      max: function (value) {
        // 直接使用数据的最大值，确保所有数据都能显示
        return value.max
      },
      // 确保边界值显示
      boundaryGap: [0, 0],
    },
    yAxis: {
      type: 'value',
      name: needsFormatting ? `值 (${formatUnit})` : '值',
      nameLocation: 'middle',
      nameGap: 50,
      nameTextStyle: {
        color: '#666',
        fontSize: 12,
      },
      axisLabel: {
        formatter: (value) => (needsFormatting ? value.toFixed(4) : value),
        color: '#666',
      },
      axisLine: {
        lineStyle: {
          color: '#ddd',
        },
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#f0f0f0',
          type: 'dashed',
        },
      },
      min: yAxisMin,
      max: yAxisMax,
      splitNumber: 8,
      minInterval: minInterval,
    },
    series: seriesData,
    backgroundColor: 'white',
  }

  chartInstance.setOption(option, true)
}

// 初始化图表
const initChart = () => {
  if (chartContainer.value) {
    chartInstance = echarts.init(chartContainer.value)

    // 设置初始图表
    const option = {
      title: {
        text: '请选择数据进行分析',
        left: 'center',
        top: 'middle',
        textStyle: {
          color: '#999',
          fontSize: 16,
        },
      },
    }

    chartInstance.setOption(option)

    // 监听窗口大小变化
    window.addEventListener('resize', () => {
      chartInstance.resize()
    })
  }
}

// 监听数据变化，自动加载字段
watch(
  [selectedDate, selectedMetric],
  async () => {
    if (selectedDate.value && selectedMetric.value && selectedConfigs.value.length > 0) {
      await loadFieldsForMetric()
    }
  },
  { immediate: false },
)
// 组件挂载后初始化
onMounted(async () => {
  await initializeData()
  nextTick(() => {
    initChart()
  })
})
</script>

<style scoped lang="scss">
// SCSS Variables
$primary-bg: #f5f5f5;
$panel-bg: #f9f9f9;
$white-bg: #fff;
$border-color: #ddd;
$light-border: #eee;
$text-color: #333;
$hover-bg: #e6f7ff;
$shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
$border-radius: 4px;
$large-border-radius: 5px;

.dashboard-container {
  padding: 20px;
  background-color: $primary-bg;
  min-height: 100vh;
  //margin-bottom: 50px;
}

.page-title {
  text-align: center;
  color: $text-color;
  margin-bottom: 30px;
  font-size: 28px;
  font-weight: 500;
}

.control-panel {
  background-color: $panel-bg;
  border: 1px solid $border-color;
  border-radius: $large-border-radius;
  padding: 20px;
  margin-bottom: 20px;
}

.control-row {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  //margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }

  @media (max-width: 768px) {
    flex-direction: column;
  }
}

.control-item {
  display: inline-block;
  vertical-align: top;
  margin: 10px;

  @media (max-width: 768px) {
    width: 100% !important;
    margin: 5px 0;
  }
}

.control-label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: $text-color;
}

.field-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding: 10px;
  background-color: $panel-bg;
  border-radius: $border-radius;
  border: 1px solid $light-border;
}

.field-title {
  font-weight: bold;
  color: $text-color;
}

.field-actions {
  display: flex;
  gap: 10px;
}

.field-toggle-container {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid $border-color;
  border-radius: $border-radius;
  padding: 10px;
  margin-top: 5px;
  background-color: $panel-bg;
}

.field-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: flex-start;
}

// 为el-checkbox-group添加样式，保持与field-grid相同的布局
:deep(.el-checkbox-group) {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: flex-start;
  width: 100%;
}

.field-checkbox {
  padding: 8px 12px;
  margin: 2px;
  background-color: $primary-bg;
  border-radius: $border-radius;
  white-space: nowrap;
  min-width: fit-content;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-shrink: 0;
  line-height: 1.4;
  min-height: 32px;
  display: flex;
  align-items: center;

  &:hover {
    background-color: $hover-bg;
  }

  // 为长文本添加tooltip效果
  :deep(.el-checkbox__label) {
    max-width: 250px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    line-height: 1.4;
    padding-bottom: 2px; // 为下降字符预留空间
  }

  // 确保复选框本身也有足够的高度
  :deep(.el-checkbox) {
    height: auto;
    line-height: 1.4;
  }

  :deep(.el-checkbox__input) {
    line-height: 1.4;
  }
}

.update-section {
  text-align: center;
  margin-top: 10px;
}

.chart-section {
  margin-bottom: 20px;
}

.chart-container {
  width: 100%;
  height: 600px;
  background-color: $white-bg;
  border-radius: $border-radius;
  box-shadow: $shadow;
}

.table-section {
  background-color: $white-bg;
  border-radius: $border-radius;
  padding: 20px;
  box-shadow: $shadow;

  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    h4 {
      margin: 0;
    }
  }
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
